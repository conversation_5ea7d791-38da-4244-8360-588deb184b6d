import React, { forwardRef } from 'react';
import { Text, Group, Rect } from 'react-konva';
import { useSelector } from 'react-redux';
import Mustache from 'mustache';

const TextElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const { mockData } = useSelector((state) => state.idDesigner);

  const getDisplayText = () => {
    if (isPreviewMode && element.text) {
      try {
        // Ensure mockData is available and has default values for common variables
        const safeData = {
          name: '<PERSON>',
          id: 'EMP001',
          department: 'Engineering',
          email: '<EMAIL>',
          phone: '******-0123',
          position: 'Senior Developer',
          company: 'Tech Corp',
          date: new Date().toLocaleDateString(),
          expiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          ...mockData, // Override with actual mock data if available
        };

        // Ren<PERSON> mustache template with mock data
        const rendered = Mustache.render(element.text, safeData);
        return rendered;
      } catch (error) {
        console.warn('Error rendering mustache template:', error, 'Text:', element.text, 'Data:', mockData);
        return element.text;
      }
    }

    // In design mode, show variables as styled chips
    if (!isPreviewMode && element.text) {
      return element.text; // We'll handle variable styling separately
    }

    return element.text || 'Text';
  };

  // Parse text to identify variables for chip-like display
  const parseTextForChips = (text) => {
    if (!text || isPreviewMode) return null;

    const parts = [];
    const regex = /\{\{([^}]+)\}\}/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(text)) !== null) {
      // Add text before the variable
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: text.slice(lastIndex, match.index),
          start: lastIndex,
          end: match.index
        });
      }

      // Add the variable
      parts.push({
        type: 'variable',
        content: match[1],
        fullMatch: match[0],
        start: match.index,
        end: match.index + match[0].length
      });

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push({
        type: 'text',
        content: text.slice(lastIndex),
        start: lastIndex,
        end: text.length
      });
    }

    return parts.length > 1 ? parts : null;
  };



  // Auto-resize font based on element dimensions
  const getAutoFontSize = () => {
    // If autoResize is explicitly disabled, always use the set fontSize
    if (element.autoResize === false) {
      return element.fontSize || 16;
    }

    // If autoResize is enabled or not set, calculate based on dimensions
    if (element.autoResize === true || element.autoResize === undefined) {
      const baseSize = Math.min(element.width, element.height) / 4;
      return Math.max(8, Math.min(baseSize, element.fontSize || 16));
    }

    // Default fallback
    return element.fontSize || 16;
  };

  const fontSize = getAutoFontSize();
  const textParts = parseTextForChips(element.text);

  // If we have variables to display as chips and not in preview mode
  if (textParts && !isPreviewMode) {
    return (
      <Group
        ref={ref}
        x={element.x}
        y={element.y}
        width={element.width}
        height={element.height}
        rotation={element.rotation || 0}
        opacity={element.opacity !== undefined ? element.opacity : 1}
        visible={element.visible !== false}
        draggable={!element.locked && !isPreviewMode}
        onClick={onClick}
        onDragEnd={onDragEnd}
      >
        {/* Background for the text area */}
        <Rect
          width={element.width}
          height={element.height}
          fill="transparent"
          stroke="rgba(79, 38, 131, 0.2)"
          strokeWidth={1}
          dash={[5, 5]}
        />

        {/* Render text parts with variable chips in a more natural flow */}
        {(() => {
          const textAlign = element.textAlign || 'left';
          let currentY = 10;
          let currentX = 10;
          const lineHeightMultiplier = element.lineHeight || 1.2;
          const lineHeight = fontSize * lineHeightMultiplier;
          const padding = 10;

          // Adjust starting X based on alignment
          if (textAlign === 'center') {
            currentX = element.width / 2;
          } else if (textAlign === 'right') {
            currentX = element.width - padding;
          }

          return textParts.map((part, index) => {
            if (part.type === 'variable') {
              const chipWidth = Math.max(part.content.length * (fontSize * 0.6) + 20, 60);
              let chipX = currentX;

              // Adjust chip position based on alignment
              if (textAlign === 'center') {
                chipX = currentX - chipWidth / 2;
              } else if (textAlign === 'right') {
                chipX = currentX - chipWidth;
              }

              // Check if chip fits on current line, if not move to next line
              if (chipX + chipWidth > element.width - padding && index > 0) {
                currentY += lineHeight;
                currentX = textAlign === 'center' ? element.width / 2 :
                          textAlign === 'right' ? element.width - padding : padding;
                chipX = textAlign === 'center' ? currentX - chipWidth / 2 :
                        textAlign === 'right' ? currentX - chipWidth : currentX;
              }

              const result = (
                <Group key={index} x={Math.max(0, chipX)} y={currentY}>
                  {/* Variable chip background */}
                  <Rect
                    width={chipWidth}
                    height={fontSize + 6}
                    fill="rgba(79, 38, 131, 0.1)"
                    stroke="#4f2683"
                    strokeWidth={1}
                    cornerRadius={12}
                  />
                  {/* Variable chip text */}
                  <Text
                    x={8}
                    y={3}
                    text={`{{${part.content}}}`}
                    fontSize={fontSize * 0.8}
                    fontFamily="monospace"
                    fill="#4f2683"
                    fontStyle="bold"
                  />
                </Group>
              );

              // Update position for next element
              if (textAlign === 'left') {
                currentX += chipWidth + 5;
              } else if (textAlign === 'right') {
                currentX -= chipWidth + 5;
              }

              return result;
            } else {
              // For text parts, render them with proper alignment
              const result = (
                <Text
                  key={index}
                  x={padding}
                  y={currentY}
                  text={part.content}
                  fontSize={fontSize}
                  fontFamily={element.fontFamily || 'Arial'}
                  fontStyle={element.fontStyle || 'normal'}
                  fill={element.color || '#000000'}
                  width={element.width - (padding * 2)}
                  wrap="word"
                  align={element.textAlign || 'left'}
                  verticalAlign="top"
                />
              );

              // Move to next line after text
              currentY += lineHeight;
              currentX = textAlign === 'center' ? element.width / 2 :
                        textAlign === 'right' ? element.width - padding : padding;

              return result;
            }
          });
        })()}
      </Group>
    );
  }

  // Default text rendering for preview mode or simple text
  const textProps = {
    ref,
    x: element.x,
    y: element.y,
    width: element.width,
    height: element.height,
    text: getDisplayText(),
    fontSize: fontSize,
    fontFamily: element.fontFamily || 'Arial',
    fontStyle: element.fontStyle || 'normal',
    fontVariant: element.fontVariant || 'normal',
    fill: element.color || '#000000',
    align: element.textAlign || 'center',
    verticalAlign: element.verticalAlign || 'middle',
    wrap: element.wrap || 'word',
    ellipsis: element.ellipsis || false,
    lineHeight: element.lineHeight || 1.2,
    rotation: element.rotation || 0,
    opacity: element.opacity !== undefined ? element.opacity : 1,
    visible: element.visible !== false,
    draggable: !element.locked && !isPreviewMode,
    onClick,
    onDragEnd,
    offsetX: 0,
    offsetY: 0,
  };

  // Add text decoration styles
  if (element.textDecoration) {
    if (element.textDecoration.includes('underline')) {
      textProps.textDecoration = 'underline';
    }
    if (element.textDecoration.includes('line-through')) {
      textProps.textDecoration = 'line-through';
    }
  }

  // Add shadow if specified
  if (element.shadow) {
    textProps.shadowColor = element.shadow.color || 'rgba(0,0,0,0.5)';
    textProps.shadowBlur = element.shadow.blur || 5;
    textProps.shadowOffset = element.shadow.offset || { x: 2, y: 2 };
    textProps.shadowOpacity = element.shadow.opacity || 0.5;
  }

  return <Text {...textProps} />;
});

TextElement.displayName = 'TextElement';

export default TextElement;
